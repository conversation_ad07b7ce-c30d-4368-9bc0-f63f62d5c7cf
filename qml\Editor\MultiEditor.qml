import QtQuick 2.15
import QtQuick.Controls 2.15
import QtGraphicalEffects 1.15
import QtQuick.Layouts 1.15
import FluentUI 1.0
import "qrc:/qml/control/common"
import "qrc:/qml/control/table"
import "qrc:/qml/control/tab"
import "qrc:/qml/NEWFBD"
import "qrc:/qml/SFCEdit"
import "qrc:/qml/STEditor"
import "qrc:/qml/LDEditor"
import "qrc:/qml/NEWLDEdit"
import "qrc:/qml/CEditor"

Rectangle {
    id: control
    width: parent ? parent.width : 800
    height: parent ? parent.height : 600
    // 当前索引文件
    property int crindex: 0
    // 打开的文件列表ListModel
    property alias fileList: openedFileNameList
    // 所属PLC设备
    property string deviceName: ""
    // 存放当前打开的文件
    property var loaders: ({})
    // 存放当前激活的文件
    property string activeFile: ""
    // 复制或剪切块元件的类型, 供LD、FBD、CFC编辑器使用
    property string copyOrCutType: ""

    signal savefile(int fid, string path)
    signal revealPosition(var position)

    //主编辑器区
    StackLayout {
        id: stacklayout
        anchors.top: control.top
        anchors.topMargin: 0
        width: control.width
        height: control.height - bar.height
        currentIndex: bar.currentIndex
        Repeater {
            model: openedFileNameList
            Loader {
                id: loader
                source: getTypeComponent(model.code)
                active: true
                onLoaded: {
                    loader.item.fileId = model.fileId
                    loader.item.fileKey = model.path
                    loader.item.deviceName = control.deviceName
                    loader.item.fileName = model.name
                    loader.item.fileCode = model.code
                    loader.item.fileType = model.type
                    loader.item.multiControl = control

                    control.savefile.connect(loader.item.savefile)
                    control.revealPosition.connect(loader.item.revealPosition)

                    loader.item.getDataBind()
                    loaders[model.fileId] = loader
                }

                Component.onDestruction: {
                    delete loaders[model.fileId]
                }
            }
        }
    }

    //文件导航按钮
    QkTabBarDown {
        id: bar
        width: control.width
        height: 40
        anchors.top: stacklayout.bottom
        anchors.topMargin: 5
        currentIndex: 0
        Repeater {
            model: openedFileNameList
            id: repeat
            QkTabButtonDown {
                id: button
                text: model.name + (model.haveChanged ? " *" : "")
                width: 150
                onClicked: {
                    activeFile = openedFileNameList.get(bar.currentIndex).name
                }
                QkButton {
                    anchors {
                        verticalCenter: parent.verticalCenter
                        right: parent.right
                        rightMargin: 5
                    }
                    width: 20
                    height: 20
                    text: "X"
                    onClicked: {
                        //关闭浏览框
                        if (model.haveChanged) {
                            messageDialog.show("是否保存当前文件！", function () {
                                control.saveCurrentFile()
                            }, function () {
                                control.closeFile(model.fileId, model.name,
                                                  model.type)
                            }, "confirm")
                        } else {
                            control.closeFile(model.fileId, model.name,
                                              model.type)
                        }
                    }
                }
            }
        }
    }

    ListModel {
        id: openedFileNameList
    }

    Connections {
        target: fbdManage

        // 文件变化保存成功，取消已更改标识
        function onFileChanged(savedfilekey) {
            updateFileFlag(savedfilekey, true)
        }

        function onNetworkChanged(filekey, networknumber, data) {
            updateFileFlag(filekey, true)
        }

        function onComponentChanged(filekey, networknumber, componentNumber, data) {
            updateFileFlag(filekey, true)
        }

        function onLineChanged(filekey, networknumber, data) {
            updateFileFlag(filekey, true)
        }
    }

    Connections {
        target: sfcManage

        // 文件变化保存成功，取消已更改标识
        function onFileChanged(savedfilekey) {
            updateFileFlag(savedfilekey, true)
        }

        function onComponentChanged(filekey, networknumber, componentNumber, data) {
            updateFileFlag(filekey, true)
        }

        function onLineChanged(filekey, networknumber, data) {
            updateFileFlag(filekey, true)
        }
    }

    function fileJump(file, specific) {
        const obj = openedFileNameList.get(bar.currentIndex)
        if (obj) {
            if (obj["code"] === "ST" || obj["code"] === "C") {
                emit: revealPosition({
                                         "lineNumber": specific,
                                         "column": 0
                                     })
            } else if (obj["code"] === "CFC" || obj["code"] === "FBD"
                       || obj["code"] === "LD") {
                emit: revealPosition(specific)
            }
        }
    }

    // 代码区放大或缩小
    function zoom(bigOrSmall) {
        const obj = openedFileNameList.get(bar.currentIndex)
        const component = loaders[obj.fileId].item
        let scale = 0

        if (obj.code === "FBD" || obj.code === "CFC") {
            scale = NEWFBDEditConfig.currentScale

            if (bigOrSmall) {
                if (scale >= 2) {
                    scale = 2
                    return
                }
                scale += 0.1
            } else {
                if (scale <= 0.2) {
                    scale = 0.2
                    return
                }
                scale -= 0.1
            }

            NEWFBDEditConfig.currentScale = scale
        } else if (obj.code === "LD") {
            if (bigOrSmall) {
                if (component.scaleFactor >= 200) {
                    component.scaleFactor = 200
                    return
                }

                component.scaleFactor += 10
            } else {
                if (component.scaleFactor <= 50) {
                    component.scaleFactor = 50
                    return
                }

                component.scaleFactor -= 10
            }
        } else if (obj.code === "SFC") {
            scale = SFCEditConfig.currentScale

            if (bigOrSmall) {
                if (scale >= 2) {
                    scale = 2
                    return
                }
                scale += 0.1
            } else {
                if (scale <= 0.2) {
                    scale = 0.2
                    return
                }
                scale -= 0.1
            }

            SFCEditConfig.currentScale = scale
        } else if (obj.code === "ST") {

        } else if (obj.code === "C") {

        }
    }

    //更新文件已修改标识
    function updateFileFlag(savedfilekey, flag) {
        let r = false
        for (var i = 0; i < openedFileNameList.count; i++) {
            const obj = openedFileNameList.get(i)
            if (obj && obj["path"] === savedfilekey) {
                obj["haveChanged"] = flag
            }
            r |= obj["haveChanged"]
        }

        isSave = r
    }

    //打开编辑器,文件列表编号,文件名称，语言类型,文件类型，文件路径
    function open(deviceName, fileId, name, code, type, filepath, password) {
        let samename = false
        let result = 1

        //先检查文件是否打开过
        for (var i = 0; i < openedFileNameList.count; i++) {
            const obj = openedFileNameList.get(i)

            if (obj["fileId"] === fileId) {
                samename = true
                crindex = i
                break
            }
        }

        if (samename) {
            //切换到当前
            bar.currentIndex = crindex
            activeFile = openedFileNameList.get(bar.currentIndex).name
        } else {
            //没有打开，则打开
            if (code === "SFC") {
                result = sfcManage.readFile(deviceName, type, name, filepath)
            } else if (code === "FBD" || code === "CFC" || code === "LD"
                       || code === "ST" || code === "C") {
                result = serviceInterface.readFile(deviceName, type, name,
                                                   filepath, fileId,
                                                   code, password)
            }

            if (result === true || result === 1) {
                openedFileNameList.append({
                                              "deviceName": deviceName,
                                              "fileId": fileId,
                                              "name": name,
                                              "code": code,
                                              "type": type,
                                              "path": filepath,
                                              "haveChanged": false
                                          })

                bar.currentIndex = openedFileNameList.count - 1
                activeFile = openedFileNameList.get(bar.currentIndex).name
            }
        }

        return result
    }

    //关闭文件
    function closeFile(fileId, name, type) {
        // console.log("closeFile", name, type, openedFileNameList.count)
        for (var i = 0; i < openedFileNameList.count; i++) {
            const obj = openedFileNameList.get(i)
            if (obj["fileId"] === fileId) {
                // 先移除掉文件保存标识再移除文件
                updateFileFlag(obj["path"], false)
                openedFileNameList.remove(i)
                bar.currentIndex = openedFileNameList.count - 1
                if (bar.currentIndex >= 0) {
                    activeFile = openedFileNameList.get(bar.currentIndex).name
                }
            }
        }
    }

    function haveAllSaved() {
        for (var i = 0; i < openedFileNameList.count; i++) {
            var obj = openedFileNameList.get(i)
            if (obj.haveChanged) {
                return false
            }
        }
        return true
    }

    //保存当前文件
    function saveCurrentFile() {
        const obj = openedFileNameList.get(bar.currentIndex)

        if(obj["haveChanged"])
        {
            if (obj) {
                if (obj["code"] === "ST") {
                    emit: control.savefile(obj["fileId"], obj["path"])
                }
                serviceInterface.saveFile(obj["fileId"], obj["path"], obj["code"])
                updateFileFlag(obj["path"], false)
            }
        }
    }

    //保存全部文件
    function saveAllFile() {
        for (var i = 0; i < openedFileNameList.count; i++) {
            const obj = openedFileNameList.get(i)
            if (obj) {
                if (obj["code"] === "ST") {
                    emit: control.savefile(obj["fileId"], obj["path"])
                }
                serviceInterface.saveFile(obj["fileId"], obj["path"],
                                          obj["code"])
                updateFileFlag(obj["path"], false)
            }
        }
    }

    function getTypeComponent(code) {
        console.log("getTypeComponent", code)
        if (code === "LD") {
            //return "qrc:/qml/NEWLDEdit/NEWLDEditorView.qml"
            return "qrc:/qml/LDEditor/LDEditorView.qml"
        } else if (code === "FBD" || code === "CFC") {
            return "qrc:/qml/NEWFBD/NEWFBDEditorView.qml"
        } else if (code === "ST") {
            return "qrc:/qml/STEditor/STEditor.qml"
        } else if (code === "SFC") {
            return "qrc:/qml/SFCEdit/SFCEditor.qml"
        } else if (code === "C") {
            return "qrc:/qml/CEditor/CEditor.qml"
        } else {
            return ""
        }
    }

    FluContentDialog {
        id: messageDialog
        property var okfunc
        property var nofunc
        title: qsTr("Tip") + (trans ? trans.transString : "")
        message: qsTr("Notification") + (trans ? trans.transString : "")
        negativeText: "不保存"
        positiveText: "保存"
        buttonFlags: FluContentDialogType.PositiveButton
        onPositiveClicked: {
            if (okfunc) {
                okfunc()
            }
        }
        onNegativeClicked: {
            if (nofunc) {
                nofunc()
            }
        }

        function show(caption, funcok, funcno, type = "info") {
            messageDialog.okfunc = funcok
            messageDialog.nofunc = funcno
            if (type === "info") {
                messageDialog.buttonFlags = FluContentDialogType.PositiveButton
            } else if (type === "confirm") {
                messageDialog.buttonFlags = FluContentDialogType.NegativeButton
                        | FluContentDialogType.PositiveButton
            }
            messageDialog.message = caption
            messageDialog.open()
        }
    }
}
