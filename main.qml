import Qt.labs.platform 1.1 as Labs11
import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 1.4 as Ctrl14
import QtQuick.Controls 2.15
import QtQuick.Dialogs 1.3 as Dia
import QtQuick.Window 2.15
import com.kdab.dockwidgets 2.0 as KDDW
import FluentUI 1.0
import "qrc:/qml"
import "qrc:/qml/CFCEdit"
import "qrc:/qml/FBDEdit"
import "qrc:/qml/NEWFBD"
import "qrc:/qml/SFCEdit"
import "qrc:/qml/STEditor"
import "qrc:/qml/CEditor"
// import "qrc:/qml/LDEdit"
import "qrc:/qml/LDEditor"
import "qrc:/qml/control"
import "qrc:/qml/control/common"
import "qrc:/qml/control/menu"
import "qrc:/qml/devicenetworkshow"
import "qrc:/qml/deviceset"
import "qrc:/qml/gv"
import "qrc:/qml/variableList"
import "qrc:/qml/monitorlist"
import "qrc:/qml/forcedvaluetable"
import "qrc:/qml/programinfo"
import "qrc:/qml/project"
import "qrc:/qml/skin"
import "qrc:/qml/variableshow"
import "qrc:/qml/onlineserver"
import "qrc:/qml/output"
import "qrc:/qml/soerecord"
import "qrc:/qml/soetable"
import "qrc:/qml/trackchart"
import "qrc:/qml/tracktable"
import "qrc:/qml/pdf"
import "qrc:/qml/Wave"
import "qrc:/qml/Wave/Canvas"
import "qrc:/SimpleUI/qml"
import "qrc:/qml/Editor"

ApplicationWindow {

    //    Image {
    //        z: 20
    //        //width: 310
    //        height: 40
    //        fillMode: Image.PreserveAspectFit
    //        anchors {
    //            top: parent.top
    //            topMargin: -50
    //            right: parent.right
    //            rightMargin: 10
    //        }
    //        source: "qrc:/assets/img/hlogo.png"
    //    }
    //mainToolBar.disableAll()
    //warning.open()
    //CloseEvent的accepted设置为false就能忽略该事件
    //closeevent.accepted = false
    id: root
    property var gv: GlobalVariable
    // 项目路径
    property string projectPath: ""
    // 是否有文件内容需要保存
    property bool isSave: false
    // 读取加密文件结果
    property int readEncryptFileResult: 1

    //删除文件后的综合处理
    function closeAllEditingDock() {
        mainDockLoader.currentEditorName = ""
        mainDockLoader.sourceComponent = null
        bottomDock.close()
        rightDock.close()
        rightTabView.toggleTab(template, 4)
        bottomTabView.toggleTab(template)
        rightTabView.toggleTab(c_functionblockList, 1)
        // rightDockLoader.sourceComponent = null
        // bottomDockLoader.sourceComponent = null
    }

    function buildErrorMsg(infoType, status, msg) {
        if (status === 1) {
            if (infoType === 10 || infoType === 11) {
                if (serviceInterface.openProject(msg)) {
                    root.openProject()
                } else {
                    messageDialog.show(`项目打开失败：${msg}`)
                }
            }
            commond.buttonStatus.disconnect(buildErrorMsg)
        } else if (status === 3) {
            messageDialog.show(`操作失败:${msg}`)
            commond.buttonStatus.disconnect(buildErrorMsg)
        }
    }

    //关闭项目综合处理
    function closeProject() {
        serviceInterface.closeProject()

        //各窗口处理 工具栏 菜单处理
        //mainToolBar.disableAll()
        GlobalVariable.isOpenProject = false
        GlobalVariable.isConnectedPLC = false
        GlobalVariable.isOnline = false
        root.stateChange()
        leftDockLoader.sourceComponent = null
        mainDockLoader.currentEditorName = ""
        mainDockLoader.sourceComponent = null

        // rightDockLoader.sourceComponent = null
        // bottomDockLoader.sourceComponent = null
        rightTabView.toggleTab(template, 1)
        rightTabView.toggleTab(template, 2)
        rightTabView.toggleTab(template, 3)
        rightTabView.toggleTab(template, 4)
        rightTabView.toggleTab(template)

        const logoInfo = bottomTabView.getComponent(1)
        logoInfo.stop()
        bottomTabView.toggleTab(template, 1)
        bottomTabView.toggleTab(template, 2)
        bottomTabView.toggleTab(template, 3)
        bottomTabView.toggleTab(template)

        commond.buttonStatus.disconnect(buildErrorMsg)
        mainMenuBar.refreshRecentList()
    }

    //打开项目综合处理
    function openedProject() {
        leftDockLoader.sourceComponent = null
        mainDockLoader.sourceComponent = null
        // rightDockLoader.sourceComponent = null
        // bottomDockLoader.sourceComponent = null
        //各窗口处理 工具栏 菜单处理
        //mainToolBar.enableAll()
        GlobalVariable.isOpenProject = true
        GlobalVariable.isConnectedPLC = false
        GlobalVariable.isOnline = false
        root.stateChange()
        mainMenuBar.refreshRecentList()
        leftDockLoader.sourceComponent = c_projectTree
        root.projectTreeDataBind(leftDockLoader.item)

        var setname = projectAndFileManage.getCurrentDeviceName()
        DeviceAndNetworkManage.setPLCSettingName(setname)
        rightTabView.toggleTab(c_functionblockList, 1)
        rightTabView.toggleTab(c_functionblockList, 2)
        rightTabView.toggleTab(c_deviceTree)

        bottomDock.open()
        bottomTabView.toggleTab("qrc:/qml/output/LogInformation.qml", 1)
        const logoInfo = bottomTabView.getComponent(1)
        logoInfo.start()

        commond.buttonStatus.connect(buildErrorMsg)
    }

    //项目树数据绑定
    function projectTreeDataBind(dt) {
        //console.log("projectTreeDataBind")
        //清空数据
        dt.clearData()
        if (projectAndFileManage.isProjectOpened()) {
            //绑定到项目树
            var jsonary = projectAndFileManage.refreshThree()
            //console.log("项目树所有数据", JSON.stringify(jsonary))
            for (var i = 0; i < jsonary.length; i++) {
                var obj = jsonary[i]
                //针对当前的激活设备进行处理
                var desc = obj.nodeDescription
                if (obj.isSelected) {
                    desc = desc + "Actived"
                    GlobalVariable.activedDevice = obj["nodeName"]
                }
                dt.addModelData(
                            obj["parentId"], obj["id"],
                            obj["nodeName"], obj["nodeType"],
                            desc + (obj["totalChildNodeType"]
                                    === "" ? "" : "(" + obj["count"] + ")"), obj["nodeIcon"],
                            obj["eventName"], obj["haveMenu"],
                            obj["isExtended"])
            }
        } else {
            console.log("projectTreeDataBind", "项目未打开")
        }
    }

    //项目树节点单击
    function projectTreeModuleSelected(nodeId, str) {
        console.debug("Project Tree Click:", nodeId, str)
    }

    //判断当前窗口是否是编辑器窗口且有文件未保存
    function checkFileNotSaved() {
        if (mainDockLoader.currentEditorName === "multiEditor"
                && !mainDockLoader.item.haveAllSaved()) {
            messageDialog.show("有文件未保存，请先保存", function () {})
            return true
        }
        return false
    }

    //项目树节点双击
    function projectTreeModuleDoubleSelected(nodeId, str, password = "") {
        if (nodeId !== 0 && str !== "") {
            var setname = projectAndFileManage.getDeviceName(nodeId)
            console.log("项目树节点单击", nodeId, str, setname)
            let regex = /\[(.+?)\]/g
            var matchlist = str.match(regex)
            if (matchlist !== null) {
                var keystr = matchlist.length > 0 ? matchlist[0] : ""
                //console.log("Project Tree:", str, keystr)
                switch (keystr) {
                case "[Add Device]":
                    root.showNewDeviceDialog()
                    break
                case "[Device Network]":
                    if (checkFileNotSaved()) {
                        return
                    }
                    rightDock.close()
                    mainDockLoader.currentEditorName = "devicenetworkShow"
                    mainDockLoader.sourceComponent = c_devicenetworkShow
                    // bottomDockLoader.sourceComponent = c_deviceNetworkEdit
                    bottomTabView.toggleTab(c_deviceNetworkEdit)
                    mainDockLoader.item.clearData()
                    mainDockLoader.item.getBindData()
                    leftDock.open()
                    mainDock.open()
                    bottomDock.open()
                    break
                case "[Device Set]":
                    if (checkFileNotSaved()) {
                        return
                    }
                    //获取当前节点对应的设备名称
                    //配置设备名称
                    if (setname === "")
                        return

                    DeviceAndNetworkManage.setPLCSettingName(setname)
                    mainDockLoader.currentEditorName = "deviceSet"
                    mainDockLoader.sourceComponent = c_deviceSet
                    // bottomDockLoader.sourceComponent = c_attributeEdit
                    bottomTabView.toggleTab(c_attributeEdit)
                    rightTabView.toggleTab(c_deviceTree)
                    // rightDockLoader.sourceComponent = c_deviceTree
                    mainDockLoader.item.clearData()
                    mainDockLoader.item.getBindData()
                    leftDock.open()
                    mainDock.open()
                    bottomDock.open()
                    rightDock.open()
                    break
                case "[Module Variable]":
                    if (checkFileNotSaved()) {
                        return
                    }
                    if (setname === "")
                        return
                    DeviceAndNetworkManage.setPLCSettingName(setname)
                    mainDockLoader.currentEditorName = "Module Variable"
                    mainDockLoader.sourceComponent = c_moduleVariable
                    mainDockLoader.item.treeBind(setname)
                    leftDock.open()
                    mainDock.open()
                    bottomDock.close()
                    rightDock.close()
                    break
                case "[Online Access]":
                    showConnectToPLCDialog()
                    break
                case "[Program]":
                case "[FunctionBlock]":
                case "[Function]":
                    if (setname !== "")
                        showNewFileDialog(nodeId, setname, keystr)
                    break
                case "[SFC_PROGRAM]":
                    bottomDock.close()
                    mainDockLoader.currentEditorName = "multiEditor"
                    mainDockLoader.sourceComponent = c_multiEditor
                    // rightDockLoader.sourceComponent = c_functionblockList
                    rightTabView.toggleTab(c_functionblockList, 1)
                    //根据项目树节点ID获取对应文件
                    var sfc_file = projectAndFileManage.getFileInfoFromProjectTreeID(
                                nodeId)
                    console.log("sfc file:", JSON.stringify(sfc_file))
                    var sfcShowB = mainDockLoader.item
                    sfcShowB.deviceName = setname
                    if (sfc_file.id) {
                        //读取文件内容
                        readEncryptFileResult = mainDockLoader.item.open(
                                    sfc_file.settingName, sfc_file.id,
                                    sfc_file.fileName, sfc_file.fatherType,
                                    sfc_file.childType,
                                    sfc_file.absolutePath, password)
                    }
                    leftDock.open()
                    if (readEncryptFileResult === 1) {
                        mainDock.open()
                        rightDock.open()
                    }
                    break
                case "[CFC_PROGRAM]":
                case "[FBD_PROGRAM]":
                case "[FBD_FUNCTIONBLOCK]":
                case "[FBD_FUNCTION]":
                    bottomDock.close()
                    mainDockLoader.currentEditorName = "multiEditor"
                    mainDockLoader.sourceComponent = c_multiEditor
                    // rightDockLoader.sourceComponent = c_functionblockList
                    rightTabView.toggleTab(c_functionblockList, 1)
                    //根据项目树节点ID获取对应文件
                    var fbd_file = projectAndFileManage.getFileInfoFromProjectTreeID(
                                nodeId)
                    console.log("fbd file:", JSON.stringify(fbd_file))
                    var fbdShowB = mainDockLoader.item
                    fbdShowB.deviceName = setname
                    if (fbd_file.id) {
                        //读取文件内容
                        readEncryptFileResult = mainDockLoader.item.open(
                                    fbd_file.settingName, fbd_file.id,
                                    fbd_file.fileName, fbd_file.fatherType,
                                    fbd_file.childType,
                                    fbd_file.absolutePath, password)
                    }

                    leftDock.open()
                    if (readEncryptFileResult === 1) {
                        mainDock.open()
                        rightDock.open()
                    }
                    break
                case "[LD_PROGRAM]":
                case "[LD_FUNCTIONBLOCK]":
                case "[LD_FUNCTION]":
                    rightDock.close()
                    bottomDock.close()
                    mainDockLoader.currentEditorName = "multiEditor"
                    mainDockLoader.sourceComponent = c_multiEditor
                    //根据项目树节点ID获取对应文件
                    var ld_file = projectAndFileManage.getFileInfoFromProjectTreeID(
                                nodeId)
                    console.log("ld_file:", JSON.stringify(ld_file))
                    var ldShowB = mainDockLoader.item
                    ldShowB.deviceName = setname
                    var obj = JSON.parse(JSON.stringify(ld_file))
                    if (ld_file.id) {
                        readEncryptFileResult = mainDockLoader.item.open(
                                    ld_file.settingName, ld_file.id,
                                    ld_file.fileName, ld_file.fatherType,
                                    ld_file.childType,
                                    obj.absolutePath, password)
                    }

                    leftDock.open()
                    if (readEncryptFileResult === 1) {
                        mainDock.open()
                    }
                    break
                case "[ST_PROGRAM]":
                case "[ST_FUNCTIONBLOCK]":
                case "[ST_FUNCTION]":
                    rightDock.close()
                    bottomDock.close()
                    mainDockLoader.currentEditorName = "multiEditor"
                    mainDockLoader.sourceComponent = c_multiEditor
                    //根据项目树节点ID获取对应文件
                    const st_file = projectAndFileManage.getFileInfoFromProjectTreeID(
                                      nodeId)
                    const multi_editor = mainDockLoader.item
                    multi_editor.deviceName = setname
                    if (st_file.id) {
                        readEncryptFileResult = mainDockLoader.item.open(
                                    st_file.settingName, st_file.id,
                                    st_file.fileName, st_file.fatherType,
                                    st_file.childType,
                                    st_file.absolutePath, password)
                    }

                    leftDock.open()
                    if (readEncryptFileResult === 1) {
                        mainDock.open()
                    }
                    break
                case "[C_PROGRAM]":
                case "[C_FUNCTION]":
                    rightDock.close()
                    bottomDock.close()
                    mainDockLoader.currentEditorName = "multiEditor"
                    mainDockLoader.sourceComponent = c_multiEditor
                    //根据项目树节点ID获取对应文件
                    const c_file = projectAndFileManage.getFileInfoFromProjectTreeID(
                                     nodeId)
                    const c_multi_editor = mainDockLoader.item
                    c_multi_editor.deviceName = setname
                    if (c_file.id) {
                        readEncryptFileResult = mainDockLoader.item.open(
                                    c_file.settingName, c_file.id,
                                    c_file.fileName, c_file.fatherType,
                                    c_file.childType,
                                    c_file.absolutePath, password)
                    }

                    leftDock.open()
                    if (readEncryptFileResult === 1) {
                        mainDock.open()
                    }
                    break
                case "[Add DataType]":
                    if (checkFileNotSaved()) {
                        return
                    }
                    rightDock.close()
                    bottomDock.close()
                    mainDockLoader.currentEditorName = "dataTypeManageShow"
                    mainDockLoader.sourceComponent = c_dataTypeManageShow
                    var dataTypeManageShowB = mainDockLoader.item
                    console.log("dataTypeManageShowB.deviceName", setname)
                    dataTypeManageShowB.deviceName = setname
                    dataTypeManageShowB.getDataBind()
                    leftDock.open()
                    mainDock.open()
                    break
                case "[Error Info]":
                    if (checkFileNotSaved()) {
                        return
                    }
                    showErrorInfo()
                    break
                case "[Monitor List]":
                    if (checkFileNotSaved()) {
                        return
                    }
                    showMonitor(setname)
                    break
                case "[ForcedValue List]":
                    if (checkFileNotSaved()) {
                        return
                    }
                    showForcedValue(setname)
                    break
                case "[Global Var]":
                    if (checkFileNotSaved()) {
                        return
                    }
                    rightDock.close()
                    mainDockLoader.sourceComponent = undefined
                    mainDockLoader.currentEditorName = "variableListShow"
                    mainDockLoader.sourceComponent = c_variableListShow
                    mainDock.title = qsTr(
                                "Resource Global") + (trans ? trans.transString : "")
                    var variableListShowB = mainDockLoader.item
                    variableListShowB.deviceName = setname
                    variableListShowB.searchowned = "Global.POE"
                    variableListShowB.searchType = "Global"
                    //variableListShowB.selectdData = []
                    variableListShowB.getBindData()

                    leftDock.open()
                    mainDock.open()
                    bottomDock.close()
                    break
                case "[IO Var]":
                    if (checkFileNotSaved()) {
                        return
                    }
                    rightDock.close()
                    mainDockLoader.sourceComponent = undefined
                    DeviceAndNetworkManage.setPLCSettingName(setname)
                    mainDockLoader.currentEditorName = "variableListShow"
                    mainDockLoader.sourceComponent = c_variableListShow
                    mainDock.title = qsTr(
                                "IO variables") + (trans ? trans.transString : "")
                    var variableListShowEV = mainDockLoader.item
                    variableListShowEV.deviceName = setname
                    variableListShowEV.searchowned = "IOM.POE"
                    variableListShowEV.searchType = "IO"
                    //variableListShowEV.selectdData = []
                    variableListShowEV.getBindData()

                    leftDock.open()
                    mainDock.open()
                    bottomDock.close()
                    break
                case "[M Var]":
                    if (checkFileNotSaved()) {
                        return
                    }
                    rightDock.close()
                    mainDockLoader.sourceComponent = undefined
                    mainDockLoader.currentEditorName = "variableListShow"
                    mainDockLoader.sourceComponent = c_variableListShow
                    mainDock.title = qsTr(
                                "M variable") + (trans ? trans.transString : "")
                    var variableListShowDEV = mainDockLoader.item
                    variableListShowDEV.deviceName = setname
                    variableListShowDEV.searchowned = "IOM.POE"
                    variableListShowDEV.searchType = "M"
                    //variableListShowDEV.selectdData = []
                    variableListShowDEV.getBindData()

                    leftDock.open()
                    mainDock.open()
                    bottomDock.close()
                    break
                case "[Program Info]":
                    if (checkFileNotSaved()) {
                        return
                    }
                    mainDockLoader.currentEditorName = "programInfoShow"
                    mainDockLoader.sourceComponent = c_programInfoShow
                    leftDock.open()
                    mainDock.open()
                    bottomDock.close()
                    rightDock.close()
                    break
                case "[Address Info]":
                    if (checkFileNotSaved()) {
                        return
                    }
                    mainDockLoader.currentEditorName = "addressInfoShow"
                    mainDockLoader.sourceComponent = c_addressInfoShow
                    leftDock.open()
                    mainDock.open()
                    bottomDock.close()
                    rightDock.close()
                    break
                case "[Search Device]":
                    //查找设备
                    showConnectToPLCDialog()
                    break
                case "[User Group]":
                    if (checkFileNotSaved()) {
                        return
                    }
                    //管理员才能进行操作
                    if (UserGroupManage.isAdminUser())
                        showUserGroupDialog()
                    else
                        messageDialog.show("管理员才能进行操作，请先进行登录", function () {
                            showUserLoginDialog()
                        })
                    break
                case "[User Login]":
                    //用户登录才能进行操作
                    showUserLoginDialog()
                    break
                case "[Add Wave]":
                    let parentid = projectAndFileManage.getProjectTreeParentNodeId(
                            setname, "[Add Wave]")
                    console.log("parentid", parentid)
                    showNewWaveFileDialog(setname, parentid)

                    break
                case "[WaveData]":
                    if (checkFileNotSaved()) {
                        return
                    }
                    bottomDock.open()
                    mainDock.open()
                    // showNewDownloadProgressDialog()
                    rightTabView.toggleTab(c_measure_cursor, 4)
                    bottomTabView.toggleTab(c_waveSetting)

                    var waveSetting = bottomTabView.getComponent()
                    waveSetting.deviceName = setname
                    waveSetting.waveFileName = str.slice(10)
                    waveSetting.updateVariable()

                    mainDockLoader.currentEditorName = "waveCanvas"
                    mainDockLoader.sourceComponent = c_waveCanvas
                    var waveCanvas = mainDockLoader.item
                    waveCanvas.waveFileName = str.slice(10)

                    waveCanvas.waveSetting = waveSetting
                    waveCanvas.setWaveConfig()

                    break
                case "[SOE]":
                    if (checkFileNotSaved()) {
                        return
                    }
                    rightDock.close()
                    bottomDock.open()
                    leftDock.open()
                    mainDock.open()
                    mainDockLoader.currentEditorName = "soeTableShow"
                    mainDockLoader.sourceComponent = c_soeTableShow
                    // bottomDockLoader.sourceComponent = c_soeRecordShow
                    bottomTabView.toggleTab(c_soeRecordShow)
                    var soeRecordDEV = bottomTabView.getComponent()
                    soeRecordDEV.deviceName = setname
                    soeRecordDEV.bindDeviceName()
                    break
                case "[Track]":
                    if (checkFileNotSaved()) {
                        return
                    }
                    rightDock.close()
                    mainDockLoader.currentEditorName = "trackListShow"
                    mainDockLoader.sourceComponent = c_trackListShow
                    // bottomDockLoader.sourceComponent = c_trackWaveChatShow
                    bottomTabView.toggleTab(c_trackWaveChatShow)
                    leftDock.open()
                    mainDock.open()
                    bottomDock.open()
                    break
                case "[Resource Info]":
                    if (checkFileNotSaved()) {
                        return
                    }
                    break
                default:

                }
            }
        }
    }

    //设备网络编辑面板属性更改事件
    function deviceNetworkValueChanged() {
        console.log("deviceNetworkValueChanged")
        var deviceNetworkEdit = bottomTabView.getComponent()
        deviceNetworkEdit.binding()
        var devicenetworkShow = mainDockLoader.item
        devicenetworkShow.getBindData()
    }

    function deviceSetDataChange() {
        console.log("deviceSetDataChange")
        var attributeEdit = bottomTabView.getComponent()
        //attributeEdit.clear()
    }

    //设备组态槽位选中事件
    function deviceSetSlotSelected() {
        //不是空槽位
        var deviceSet = mainDockLoader.item
        var attributeEdit = bottomTabView.getComponent()
        //console.log("deviceSetSlotSelected=", deviceSet.selectedID,deviceSet.deviceID)
        if (deviceSet.deviceID !== 0) {
            //根据deviceSet.deviceID获取数据进行属性编辑绑定
            //先获取数据再 清空后绑定属性面板
            var jsonobj2 = DeviceAndNetworkManage.getDeviceSlot(
                        deviceSet.deviceID)
            //console.log("DeviceAndNetworkManage.getDeviceSlot",JSON.stringify(jsonobj2))
            attributeEdit.clear()
            attributeEdit.selectedID = deviceSet.selectedID
            attributeEdit.deviceID = deviceSet.deviceID
            attributeEdit.init(jsonobj2, true, 0)
        } else {
            attributeEdit.clear()
        }
    }

    //设备树添加模块
    function devictTreeModuleAdd(devId, str, version) {
        //console.log("devictTreeModuleAdd:", devId, str, version)
        //添加模块
        var deviceSet = mainDockLoader.item
        if (deviceSet && (deviceSet instanceof DeviceSetEditor)) {
            var addOK = deviceSet.addModule(devId)
            if (addOK) {
                //console.log("deviceSet.selectedID2", deviceSet.selectedID)

                //新增后选中下一个槽位
                var sps = deviceSet.selectedID.split(",")
                if (sps.length === 3) {
                    var sn = parseInt(sps[2])
                    sn = sn + 1
                    if (sn > 32)
                        sn = 32

                    deviceSet.selectedID = sps[0] + "," + sps[1] + "," + sn.toString()
                }
                //获取当前模块的配置信息
                var attributeEdit = bottomTabView.getComponent()
                if (!deviceSet.isEmptySlot()) {
                    attributeEdit.clear()
                    attributeEdit.selectedID = deviceSet.selectedID
                    var jsonobj2 = DeviceAndNetworkManage.getDeviceSlot(
                                deviceSet.selectedID)
                    //console.log("DeviceAndNetworkManage.getDeviceSlot", deviceSet.selectedID, JSON.stringify(jsonobj2))
                    attributeEdit.init(jsonobj2, true)
                } else {
                    attributeEdit.clear()
                }
            } else {
                messageDialog.show(
                            "模块插入要求：\n控制器模块只能插入槽0,其它槽只能插入非控制器模块。\n高速扩展模块只能插在控制器的高速扩展口上，其中冗余模块指定扩展槽1，网关模块指定扩展槽1和扩展槽2。\n独立模块不能插在基板上。",
                            function () {})
            }
        }
    }

    // 删除选中的变量
    function deleteVariable(deleteData) {
        for (var i = 0; i < deleteData.length; i++) {
            VariableManage.deleteVariable(deleteData[i])
        }
    }

    //工具栏点击事件
    function toolBarTriggered(eventCode) {
        console.log("toolBar:", eventCode)
        switch (eventCode) {
        case "newproject":
            showNewProjectDialog()
            break
        case "openproject":
            openProjectDialog.open()
            break
        case "saveproject":
            //保存项目
            if (projectAndFileManage.isProjectOpened()) {

            }
            break
        case "printfile":
            console.log("printfile")
            break
        case "Simulation":
            //仿真
            showSimulationDialog()
            break
        case "goonline":
            root.actionOnline()
            break
        case "gooffline":
            root.actionOffline()
            break
        case "downloadtoram":
            root.actionDownloadSystem()
            break
        case "setvariable":
            //webShowDialog.show()
            break
        case "savefile":
            // 保存文件
            if (mainDockLoader.currentEditorName === "multiEditor"
                    || mainDockLoader.currentEditorName === "multiCFCEditor"
                    || mainDockLoader.currentEditorName === "multiFBDEditor"
                    || mainDockLoader.currentEditorName === "multiLDEditor"
                    || mainDockLoader.currentEditorName === "multiSFCEditor")
                mainDockLoader.item.saveCurrentFile()

            break
        case "cut":
            // 剪切
            shortcutController.shear(multi_shortcuts.addContentToClipboard)
            break
        case "copy":
            // 复制
            shortcutController.copy(multi_shortcuts.addContentToClipboard)
            break
        case "paste":
            // 粘贴
            shortcutController.paste(multi_shortcuts.getClipbooardContent)
            break
        case "delete":
            // 删除
            shortcutController.del()
            break
        case "undo":
            // 撤销
            shortcutController.undo(multi_shortcuts.getSnapshot)
            break
        case "redo":
            // 反撤销
            shortcutController.redo(multi_shortcuts.getSnapshot)
            break
        case "zoomin":
            // 放大
            zoom(true)
            break
        case "zoomout":
            // 缩小
            zoom(false)
            break
        case "build":
            //构建项目
            showBuildProgressDialog()
            break
        case "moveup":
            //showSetPasswordDialog("ddddddx")
            break
        case "movedown":
            //showChangePasswordDialog("eeeeee")
            break
        case "moniter":
            //showConfirmPasswordDialog("xxxxx")
            break
        case "checkall":
            break
        case "stop":
            //showUserLoginDialog()
            break
        case "hardconfig":
            if (GlobalVariable.activedDevice !== "") {
                //打印GlobalVariable.activeDevice
                console.log("GlobalVariable.activedDevice",
                            GlobalVariable.activedDevice)
                showHardwareConfigDialog()
            } else {
                messageDialog.show("请先添加一个设备!")
            }
            //showWaitOnFrame()
            break
        case "Network Search":
            showConnectToPLCDialog()
            break
        case "hsplit":
            break
        case "vsplit":
            break
        case "upload":
            showPopupCenter(uploadDialogComponent)
            break
        case "download":
            showPopupCenter(downloadDialogComponent)
            break
        default:
            break
        }
    }

    //工具栏数据绑定
    function toolBarDataBind(toolBar) {
        var jsonary = toolbarmag.getToolBarData()
        toolBar.addModelDataWithJSONArray(jsonary)
    }

    //菜单点击事件
    function menuItemTriggered(eventCode) {
        console.log("menu:", eventCode)
        let regex = /\[(.+?)\]/g
        var matchlist = eventCode.match(regex)
        if (matchlist !== null) {
            var keystr = matchlist.length > 0 ? matchlist[0] : ""
            //console.log("menuItemTriggered:", eventCode, keystr)
            switch (keystr) {
            case "[recentlist]":
                var pf = eventCode.replace("[recentlist]", "")
                console.log("matchlist[1]", pf)
                if (serviceInterface.openProject(pf)) {
                    //根据返回结果进行当前项目切换
                    mainMenuBar.closeAllMenu()
                    root.openedProject()
                } else {
                    messageDialog.show("工程打开错误，请先检查工程路径以及工程内容是否有误！",
                                       function () {
                                           //确认后关闭对话框
                                           popupCenter.close()
                                       })
                    projectAndFileManage.deleteRecentProject(pf)
                    //加载最近项目列表
                    mainMenuBar.refreshRecentList()
                }
                break
            default:
                break
            }
        } else {
            switch (eventCode) {
            case "newproject":
                showNewProjectDialog()
                break
            case "openproject":
                openProjectDialog.open()
                break
            case "saveproject":
                //保存项目
                if (projectAndFileManage.isProjectOpened()) {

                }
                break
            case "packageproject":
                //打包工程
                packageproject()
                break
            case "unpackageproject":
                //解包工程
                unpackageproject()
                break
            case "transplantproject":
                // 工程另存为
                saveAsProject()
                // showTransplantProjectDialog()
                break
            case "printproject":

                //打印工程
                break
            case "projectinfo":
                //工程信息
                showProjectInfoDialog()
                break
            case "projectCheck":
                //工程检查
                showProjectCheckDialog()
                break
            case "protectproject":
                //保护工程
                showProtectProjectDialog()
                break
            case "decryptproject":
                //解除保护
                showProtectProjectDialog()
                break
            case "deletedevice":
                // 删除设备
                deleteDevice()
                break
            case "closeproject":
                //关闭项目
                closeProject()
                break
            case "clearlist":
                projectAndFileManage.clearRecentProject()
                mainMenuBar.refreshRecentList()
                break
            case "Save All":
                if (mainDockLoader.currentEditorName === "multiEditor"
                        || mainDockLoader.currentEditorName === "multiCFCEditor"
                        || mainDockLoader.currentEditorName === "multiFBDEditor"
                        || mainDockLoader.currentEditorName === "multiLDEditor")
                    mainDockLoader.item.saveAllFile()

                console.log("serviceInterface.saveALL()",
                            serviceInterface.saveALL())
                break
            case "Build":
                // console.log("serviceInterface.build()",
                //             serviceInterface.build())
                showBuildProgressDialog()
                break
            case "Network Search":
                showConnectToPLCDialog()
                break
            case "Go Online":
                //转至在线
                root.actionOnline()
                break
            case "Go Offline":
                //转至离线
                root.actionOffline()
                break
            case "Download To RAM":
                //下载至RAM
                root.actionDownloadRam()
                break
            case "Download To Flash":
                //下载至Flash
                root.actionDownloadSystem()
                break
            case "Write Firmware":
                //写入固件
                root.actionWriteFirmware()
                break
            case "Upload Program":
                // 从设备上传程序
                root.actionUploadProgram()
                break
            case "Upload Hardware":
                // 从设备上传硬件和程序
                root.actionUploadHardware()
                break
            case "Restart PLC":
                //重启
                root.actionRestart()
                break
            case "Cold Start":
                //冷启动
                root.actionColdBoot()
                break
            case "Hot Start":
                //热启动
                root.actionHotBoot()
                break
            case "Stop PLC":
                root.actionStop()
                //停止
                break
            case "Online Diagnose":
                //在线诊断
                showErrorInfo()
                break
            case "Simulation":
                //仿真
                showSimulationDialog()
                break
            case "Factory Settings":
                //恢复出厂设置
                break
            case "Project Tree View":
                toggleDockWidget(leftDock)
                break
            case "Main View":
                toggleDockWidget(mainDock)
                break
            case "Device Tree View":
                toggleDockWidget(rightDock)
                break
            case "Message View":
                toggleDockWidget(bottomDock)
                break
            case "Online Server Panel":
                showOnlineServerPanel()
                break
                // 监控表
            case "Monitor List":
                showMonitor(projectAndFileManage.getCurrentDeviceName())
                break
                // 强制表
            case "ForcedValue List":
                showForcedValue(projectAndFileManage.getCurrentDeviceName())
                break
                // 报警信息导出
            case "Errorlog Export":
                exportDialog.open()
                break
            case "Reference Info":
                console.log("Reference Info")
                break
            case "ParamBackup":
                proc.start_nb_process("ParamBackupTool.exe", "")
                break
            case "NormalSkin":
                SkinManager.currentSkin = SkinNormal
                break
            case "RedSkin":
                SkinManager.currentSkin = SkinRed
                break
            case "BlueSkin":
                SkinManager.currentSkin = SkinSkyBlue
                break
            case "Chinese":
                trans.setCurrentLang("简体中文")
                mainMenuBar.updateTextWithLanguage()
                mainMenuBar.refreshRecentList()
                break
            case "English":
                trans.setCurrentLang("English")
                mainMenuBar.updateTextWithLanguage()
                mainMenuBar.refreshRecentList()
                break
            case "Security module":
                //防火墙
                singletonWindow.open()
                break
            case "About":
                showAboutDialog()
                break
                // 帮助信息
            case "Help Info":
                showHelpInfo()
                break
            case "Quit":
                Qt.quit()
                break
                // 包导入
            case "Package Import":
                packageImport()
                break
                // 库导入
            case "Library Import":
                libraryImport()
                break
            default:
                break
            }
        }
    }

    //菜单绑定数据
    function menuDataBind(menu) {
        var jsonary = menumag.getMenuData()
        mainMenuBar.addModelDataWithJSONArray(jsonary)
    }

    //软件状态字切换
    function stateChange() {
        GlobalVariable.updateStateKey()
        mainToolBar.changeState(GlobalVariable.stateKey)
        mainMenuBar.changeState(GlobalVariable.stateKey)
    }

    //Dock显示与关闭切换
    function toggleDockWidget(dw) {
        if (dw.dockWidget.isOpen())
            dw.dockWidget.forceClose()
        else
            dw.dockWidget.show()
    }

    // 代码区放大或缩小
    function zoom(bigOrSmall) {
        // 只对代码区有用
        if (mainDockLoader.currentEditorName === "multiEditor") {
            mainDockLoader.item.zoom(bigOrSmall)
        }
    }

    //弹窗显示
    function showPopupCenter(raiseItem) {
        popupCenter.raiseItem = raiseItem
        popupCenter.open()
    }

    //显示关于窗口
    function showAboutDialog() {
        var c_aboutDialog = Qt.createComponent("qrc:/qml/MainAboutDialog.qml")
        if (c_aboutDialog.status === Component.Ready)
            root.showPopupCenter(c_aboutDialog)
    }

    property Component helpInfoDialogComponent: Qt.createComponent(
                                                    "qrc:/qml/MainHelpInfoDialog.qml")
    property var helpInfoDialogInstance: null

    // 显示帮助信息
    function showHelpInfo() {
        root.showPopupCenter(null)
        if (!helpInfoDialogInstance) {
            if (helpInfoDialogComponent.status === Component.Ready) {
                helpInfoDialogInstance = helpInfoDialogComponent.createObject(
                            root)
                if (helpInfoDialogInstance) {
                    // 连接关闭信号
                    helpInfoDialogInstance.onClosed.connect(function () {
                        helpInfoDialogInstance.visible = false
                    })
                }
            } else if (helpInfoDialogComponent.status === Component.Error) {
                console.log("Error loading component:",
                            helpInfoDialogComponent.errorString())
            }
        } else {
            helpInfoDialogInstance.visible = true
        }
    }

    // 显示故障信息
    function showErrorInfo() {
        mainDockLoader.currentEditorName = "errorInfoTableShow"
        mainDockLoader.sourceComponent = c_errorInfoTableShow
        leftDock.open()
        mainDock.open()
        bottomDock.close()
        rightDock.close()
    }

    // 显示监控表
    function showMonitor(setname) {
        rightDock.close()
        //重新读文件
        debugManage.readFile()
        // monitor.init()
        mainDockLoader.currentEditorName = "monitorMShow"
        mainDockLoader.sourceComponent = c_monitorMShow
        var monitorShowB = mainDockLoader.item
        monitorShowB.deviceName = setname
        monitorShowB.getDataBind()
        leftDock.open()
        mainDock.open()
        bottomDock.close()
        rightDock.close()
    }

    // 显示强制表
    function showForcedValue(setname) {
        rightDock.close()
        mainDockLoader.currentEditorName = "monitorCustomShow"
        mainDockLoader.sourceComponent = c_monitorCustomShow
        var monitorCustomShowB = mainDockLoader.item
        monitorCustomShowB.deviceName = setname
        monitorCustomShowB.getDataBind()
        leftDock.open()
        mainDock.open()
        bottomDock.close()
        rightDock.close()
    }

    // 包导入
    function packageImport() {
        showExtraInfo("Install Package")
    }

    // 库导入
    function libraryImport() {
        showExtraInfo("Library Library")
    }

    // 编译出错文件跳转
    function fileJump(file, specific) {
        if (projectAndFileManage.isProjectOpened()) {
            const fileInfo = projectAndFileManage.getFileByName(file)
            // 文件后缀
            const fileSuffix = file.split(".")[1]
            // 文件类型
            const childType = fileInfo.childType
            const eventName = "[" + fileSuffix + "_" + childType + "]"

            // 打开文件
            projectTreeModuleDoubleSelected(fileInfo.projectTreeNodeId,
                                            eventName)
            // 选中打开的文件
            leftDockLoader.item.setSelectedNode(fileInfo.projectTreeNodeId,
                                                eventName)

            mainDockLoader.item.fileJump(file, specific)
        }
    }

    // 可拖动窗口
    DraggableWindow {
        id: draggableWindow
    }

    // 显示附加信息窗口
    function showExtraInfo(navigate) {
        const c_extraInfo = Qt.createComponent("qrc:/qml/ExtraInfo.qml")
        if (c_extraInfo.status === Component.Ready) {
            if (draggableWindow.popupWindows.hasOwnProperty("extraInfoPanel")) {
                draggableWindow.closePopup("extraInfoPanel")
                return
                // 如果已经存在附加信息窗口，则不再创建
            }
            const extra_info_popup = draggableWindow.createPopup({
                                                                     "id": "extraInfoPanel",
                                                                     "title": "Extra Info",
                                                                     "contentComponent": c_extraInfo
                                                                 })

            extra_info_popup.itemCom.currentSelectNavigate = navigate
        }
    }

    // 显示仿真窗口
    function showSimulationDialog() {
        const c_simulationPanel = Qt.createComponent(
                                    "qrc:/qml/SimulationPanel.qml")
        if (c_simulationPanel.status === Component.Ready) {
            if (draggableWindow.popupWindows.hasOwnProperty(
                        "simulationPopup")) {
                draggableWindow.closePopup("simulationPopup")
                return
                // 如果已经存在仿真窗口，则不再创建
            }
            const simu_popup = draggableWindow.createPopup({
                                                               "id": "simulationPopup",
                                                               "title": qsTr("Simulation"),
                                                               "contentComponent": c_simulationPanel
                                                           })
        }
    }

    // 显示在线服务面板
    function showOnlineServerPanel() {
        const c_onlineServer = Qt.createComponent(
                                 "qrc:/qml/onlineserver/OnlineServer.qml")
        if (c_onlineServer.status === Component.Ready) {
            if (draggableWindow.popupWindows.hasOwnProperty(
                        "onlineServerPopup")) {
                draggableWindow.closePopup("onlineServerPopup")
                return
                // 如果已经存在仿真窗口，则不再创建
            }
            const online_server_popup = draggableWindow.createPopup({
                                                                        "id": "onlineServerPopup",
                                                                        "title": qsTr("Online Server Panel") + (trans ? trans.transString : ""),
                                                                        "contentComponent": c_onlineServer
                                                                    })
        }
    }

    //显示新建文件窗口
    function showNewFileDialog(nodeId, deviceName, keystr) {
        console.log("showNewFileDialog", nodeId, deviceName)
        var c_newFile = Qt.createComponent("qrc:/qml/project/NewFile.qml")
        if (c_newFile.status === Component.Ready) {
            root.showPopupCenter(c_newFile)
            popupCenter.loadercenter.item.setnodeID(nodeId, deviceName)
            popupCenter.loadercenter.item.selectFileType(keystr)
            popupCenter.loadercenter.item.newFileOk.connect(function () {
                console.log("newFileOk")
                root.projectTreeDataBind(leftDockLoader.item)
            })
        } else {
            console.log("Qt.createComponent(qrc:/qml/project/NewFile.qml) Error")
        }
    }

    //显示硬件配置窗口
    function showHardwareConfigDialog() {
        var c_hardwareConfig = Qt.createComponent(
                    "qrc:/qml/project/HardwareConfig.qml")
        if (c_hardwareConfig.status === Component.Ready)
            root.showPopupCenter(c_hardwareConfig)
    }

    //显示连接到PLC窗口
    function showConnectToPLCDialog() {
        var c_connectToPLC = Qt.createComponent(
                    "qrc:/qml/project/ConnectToPLC.qml")
        if (c_connectToPLC.status === Component.Ready) {
            root.showPopupCenter(c_connectToPLC)
            popupCenter.loadercenter.item.close.connect(function () {
                //console.log("root.stateChange()")
                root.stateChange()
            })
        }
    }

    //显示新建项目窗口
    function showNewProjectDialog() {
        var c_newProject = Qt.createComponent("qrc:/qml/project/NewProject.qml")
        if (c_newProject.status === Component.Ready) {
            root.showPopupCenter(c_newProject)
            //链接信号
            popupCenter.loadercenter.item.newProjectOk.connect(
                        function (filename) {
                            if (serviceInterface.openProject(filename)) {
                                root.openedProject()
                            }
                            //console.log("newProjectOk", filename)
                        })
        }
    }
    //下载栏
    function showNewDownloadProgressDialog() {// var c_newWaveFile = Qt.createComponent(
        //             "qrc:/qml/Wave/ProgressDialog.qml")
        // if (c_newWaveFile.status === Component.Ready) {
        //     root.showPopupCenter(c_newWaveFile)
        //     popupCenter.loadercenter.item.startManual(
        //         "Download",
        //         "录波下载",
        //         function () {
        //             console.log("确定按钮触发后执行事件")
        //         },
        //         function () {
        //             console.log("取消按钮触发后执行事件")
        //         })

        //            popupCenter.loadercenter.item.newFileOk.connect(function () {
        //                console.log("newFileOk")
        //                root.projectTreeDataBind(leftDockLoader.item)
        //            })
        // }
    }

    function showBuildProgressDialog() {
        //检查文件变量功能块是否具备编译条件
        const error = serviceInterface.checkForCompile(
                        GlobalVariable.activedDevice)
        if (error !== "") {
            messageDialog.show(error)
            return
        }
        const flag = serviceInterface.createCompileEnvironment()
        if (!flag) {
            messageDialog.show("创建编译环境失败!")
            return
        }
        OutputManage.clear()
        serviceInterface.exportAllVariableCSV2(GlobalVariable.activedDevice)
        compileManager.build_e2000()
        const c_buildProgress = Qt.createComponent(
                                  "qrc:/qml/Wave/ProgressDialog.qml")
        if (c_buildProgress.status === Component.Ready) {
            root.showPopupCenter(c_buildProgress)
            popupCenter.loadercenter.item.showConfirmBtn = false
            popupCenter.loadercenter.item.showCancelBtn = true
            popupCenter.loadercenter.item.autoClose = false
            popupCenter.loadercenter.item.cancelAction = () => {
                compileManager.compileProgress.disconnect(updateProgress)
                compileManager.stop()
            }
            function updateProgress(total, current) {
                if (current <= 0) {
                    compileManager.compileProgress.disconnect(updateProgress)
                    popupCenter.close()
                    bottomDock.open()
                    messageDialog.show("编译失败，详情请查看日志信息")
                    compileManager.printErrMsg(current)
                } else {
                    if (total === current)
                        popupCenter.loadercenter.item.showConfirmBtn = true
                    popupCenter.loadercenter.item.setProgressValue(
                                (current / total).toFixed(2))
                }
            }
            popupCenter.loadercenter.item.startManual({
                                                          "label": "编译中",
                                                          "title": "Build",
                                                          "onConfirm": function () {
                                                              compileManager.compileProgress.disconnect(
                                                                          updateProgress)
                                                          },
                                                          "onCancel": function () {
                                                              compileManager.compileProgress.disconnect(
                                                                          updateProgress)
                                                          }
                                                      })
            compileManager.compileProgress.connect(updateProgress)
        }
    }

    function showVariableSelectDialog() {
        var c_newFile = Qt.createComponent(
                    "qrc:/qml/variableshow/VariableSearchPanel.qml")
        if (c_newFile.status === Component.Ready) {
            root.showPopupCenter(c_newFile)
            popupCenter.loadercenter.item.deviceName = projectAndFileManage.getCurrentDeviceName()
            popupCenter.loadercenter.item.searchowned = ["Global.POE", "IOM.POE"]
            popupCenter.loadercenter.item.searchType = ["Global", "IO", "M"]
            popupCenter.loadercenter.item.getBindData()
            popupCenter.loadercenter.item.selectVariableOk.connect(
                        function (data) {
                            console.log("selectVariableOk",
                                        JSON.stringify(data))
                        })
        } else {
            console.log("Qt.createComponent(qrc:/qml/variableshow/VariableSearchPanel.qml) Error")
        }
    }

    // 显示新增录波窗口
    function showNewWaveFileDialog(devName, parentid) {
        var c_newWaveFile = Qt.createComponent("qrc:/qml/Wave/AddWaveFile.qml")
        if (c_newWaveFile.status === Component.Ready) {
            root.showPopupCenter(c_newWaveFile)
            popupCenter.loadercenter.item.setnodeID(parentid, devName)
            popupCenter.loadercenter.item.newFileOk.connect(function () {
                console.log("newFileOk")
                root.projectTreeDataBind(leftDockLoader.item)
            })
        }
    }

    //打包工程
    function packageproject() {
        var c_packingWorks = Qt.createComponent(
                    "qrc:/qml/project/PackingWorks.qml")
        if (c_packingWorks.status === Component.Ready) {
            root.showPopupCenter(c_packingWorks)
        }
    }

    //解包工程
    function unpackageproject() {
        var c_unPackingWorks = Qt.createComponent(
                    "qrc:/qml/project/UnPackingWorks.qml")
        if (c_unPackingWorks.status === Component.Ready) {
            root.showPopupCenter(c_unPackingWorks)
        }
    }

    // 工程另存为
    function saveAsProject() {
        const projectInfo = projectAndFileManage.getCurrentProjectInfo()
        const projectName = projectInfo.ProjectName
        savaProjectPath.currentFile = "file:///" + projectName + ".proj"
        savaProjectPath.open()
    }

    //显示移植项目窗口
    function showTransplantProjectDialog() {

        //链接信号
        var c_transplantProject = Qt.createComponent(
                    "qrc:/qml/project/TransplantProject.qml")
        if (c_transplantProject.status === Component.Ready)
            root.showPopupCenter(c_transplantProject)
    }

    //显示项目信息窗口
    function showProjectInfoDialog() {

        //链接信号
        var c_ProjectInfo = Qt.createComponent(
                    "qrc:/qml/project/ProjectInfo.qml")
        if (c_ProjectInfo.status === Component.Ready)
            root.showPopupCenter(c_ProjectInfo)
    }

    //显示项目检查窗口
    function showProjectCheckDialog() {

        //链接信号
        var c_ProjectCheck = Qt.createComponent(
                    "qrc:/qml/project/ProjectCheck.qml")
        if (c_ProjectCheck.status === Component.Ready)
            root.showPopupCenter(c_ProjectCheck)
    }

    //显示项目检查窗口
    function showProtectProjectDialog() {

        //链接信号
        var c_ProtectProject = Qt.createComponent(
                    "qrc:/qml/project/ProtectXM.qml")
        if (c_ProtectProject.status === Component.Ready)
            root.showPopupCenter(c_ProtectProject)
    }

    // 删除设备
    function deleteDevice() {
        var jsonary = projectAndFileManage.refreshThree()
        var name
        var id
        var isNull = true
        for (var i = 0; i < jsonary.length; i++) {
            var obj = jsonary[i]
            var desc = obj.nodeDescription
            if (obj.isSelected) {
                id = obj.id
                name = obj.nodeName
                isNull = false
            }
        }

        if (!isNull) {
            messageDialog.show("是否删除该设备" + name + ",相关的组态配置和程序将会一并删除?",
                               function () {
                                   //先删除设备组态
                                   DeviceAndNetworkManage.deleteDevice(name)
                                   VariableManage.deleteDeviceDataType(name)
                                   VariableManage.deleteDeviceMonitor(name)
                                   VariableManage.deleteDeviceForced(name)
                                   VariableManage.deleteDeviceVariable(name)
                                   projectAndFileManage.deleteDeviceNode(id)
                                   root.gv.isDSCheckCompleted = false
                                   closeAllEditingDock()
                                   root.projectTreeDataBind(leftDockLoader.item)
                               }, function () {
                                   console.log("confrim no1")
                               }, "confirm")
        }
    }

    //显示新建设备窗口
    function showNewDeviceDialog() {
        var c_newDevice = Qt.createComponent(
                    "qrc:/qml/project/NewDeviceFolder.qml")
        if (c_newDevice.status === Component.Ready) {
            root.showPopupCenter(c_newDevice)
            //链接信号
            popupCenter.loadercenter.item.newDeviceOk.connect(
                        function (filename) {
                            root.projectTreeDataBind(leftDockLoader.item)
                            var projectDir = projectAndFileManage.getCurrentProjectPath(
                                        ) + "/" + projectAndFileManage.getCurrentDeviceName()
                            //resman.initRes(projectDir, projectAndFileManage.getCurrentDeviceName())
                            console.log("projDir", projectDir)
                        })
        }
    }

    //显示设置密码窗口
    function showSetPasswordDialog(objname, callback) {
        var c_setpassword = Qt.createComponent(
                    "qrc:/qml/project/SetPassword.qml")
        if (c_setpassword.status === Component.Ready) {
            root.showPopupCenter(c_setpassword)
            popupCenter.loadercenter.item.passwordObject = objname
            //链接信号
            popupCenter.loadercenter.item.newPasswordOk.connect(callback)
        }
    }

    //显示更改密码窗口
    function showChangePasswordDialog(objname, callback) {
        var c_chnagepassword = Qt.createComponent(
                    "qrc:/qml/project/ChangePassword.qml")
        if (c_chnagepassword.status === Component.Ready) {
            root.showPopupCenter(c_chnagepassword)
            popupCenter.loadercenter.item.passwordObject = objname
            //链接信号
            popupCenter.loadercenter.item.changePasswordOk.connect(callback)
        }
    }

    //显示密码确认窗口
    function showConfirmPasswordDialog(objname, callback) {
        var c_confirmpassword = Qt.createComponent(
                    "qrc:/qml/project/ConfirmPassword.qml")
        if (c_confirmpassword.status === Component.Ready) {
            root.showPopupCenter(c_confirmpassword)
            popupCenter.loadercenter.item.passwordObject = objname
            //链接信号
            popupCenter.loadercenter.item.confirmPasswordOk.connect(callback)
        }
    }

    //显示安全设置窗口
    function showProtectXmDialog() {
        var c_protectXM = Qt.createComponent("qrc:/qml/project/ProtectXM.qml")
        if (c_protectXM.status === Component.Ready)
            root.showPopupCenter(c_protectXM)
    }

    //显示用户与用户组管理窗口
    function showUserGroupDialog() {
        var c_userGroup = Qt.createComponent(
                    "qrc:/qml/project/NewUserGroup.qml")
        if (c_userGroup.status === Component.Ready)
            root.showPopupCenter(c_userGroup)
    }

    //显示用户登录窗口
    function showUserLoginDialog() {
        var c_userLogin = Qt.createComponent(
                    "qrc:/qml/project/NewUserLogin.qml")
        if (c_userLogin.status === Component.Ready)
            root.showPopupCenter(c_userLogin)
    }

    //根据项目树中文件列表获取文件目录
    function getFileNameFromProjectItem(str) {
        let regex = /\[(.+?)\]/g
        var matchlist = str.match(regex)
        if (matchlist !== null) {
            var keystr = matchlist.length > 0 ? matchlist[0] : ""
            return str.replace(keystr, "")
        }
        return ""
    }

    //显示全局等待窗口
    function showWaitOnFrame() {
        var c_waitOnFrame = Qt.createComponent("qrc:/qml/WaitOnFrame.qml")
        if (c_waitOnFrame.status === Component.Ready)
            root.showPopupCenter(c_waitOnFrame)
    }

    function showFirmwareUpdateDialog(sid) {
        console.log("main firemwareVersionClicked", sid)
        var c_firmwareUpdateDialog = Qt.createComponent(
                    "qrc:/qml/FirmwareUpdateDialog.qml")
        if (c_firmwareUpdateDialog.status === Component.Ready)
            root.showPopupCenter(c_firmwareUpdateDialog)
    }

    //上线操作
    function actionOnline() {
        commond.connect3568ByTcp()
        GlobalVariable.isOnline = true
        root.stateChange()
    }

    //离线操作
    function actionOffline() {
        commond.disConnect3568ByTcp()
        GlobalVariable.isOnline = false
        root.stateChange()
    }

    //下载至ram
    function actionDownloadRam() {
        const projectPath = projectAndFileManage.getCurrentProjectPath()
        const deviceName = projectAndFileManage.getCurrentDeviceName()
        const filePath = projectPath + "/" + deviceName + "/oneos.bin"
        if (serviceInterface.fileExists(filePath)) {
            commond.downloadProjectInfo_Ram()
        } else {
            messageDialog.show("target does not exist")
        }
    }

    //下载至system
    function actionDownloadSystem() {
        const projectPath = projectAndFileManage.getCurrentProjectPath()
        const deviceName = projectAndFileManage.getCurrentDeviceName()
        const filePath = projectPath + "/" + deviceName + "/oneos.bin"
        if (serviceInterface.fileExists(filePath)) {
            commond.downloadProjectInfo_Flash()
        } else {
            messageDialog.show("target does not exist")
        }
    }

    // 写入固件
    function actionWriteFirmware() {
        commond.writeFirmware()
    }

    // 从设备上传程序
    function actionUploadProgram() {
        const c_uploadProgram = Qt.createComponent(
                                  "qrc:/qml/project/UploadProgram.qml")
        if (c_uploadProgram.status === Component.Ready) {
            root.showPopupCenter(c_uploadProgram)
        }
        popupCenter.loadercenter.item.confirmUpload.connect(
                    function (name, path) {
                        popupCenter.close()
                        closeProject()
                        commond.buttonStatus.disconnect(buildErrorMsg)
                        commond.uploadExe(path)
                    })
    }

    function actionUploadHardware() {
        const c_uploadProgram = Qt.createComponent(
                                  "qrc:/qml/project/UploadProgram.qml")
        if (c_uploadProgram.status === Component.Ready) {
            root.showPopupCenter(c_uploadProgram)
        }
        popupCenter.loadercenter.item.confirmUpload.connect(
                    function (name, path) {
                        popupCenter.close()
                        closeProject()
                        commond.buttonStatus.disconnect(buildErrorMsg)
                        commond.uploadExe(path)
                    })
    }

    // 文件加密
    function fileEncryot(fileId, fileName, fileType) {
        const c_fileEncrypt = Qt.createComponent("qrc:/qml/FileEncrypt.qml")

        if (c_fileEncrypt.status === Component.Ready) {
            root.showPopupCenter(c_fileEncrypt)
            popupCenter.loadercenter.item.fileId = fileId
            popupCenter.loadercenter.item.closeFile.connect(() => {
                                                                if (mainDockLoader.currentEditorName
                                                                    === "multiEditor") {
                                                                    mainDockLoader.item.closeFile(
                                                                        fileId,
                                                                        fileName,
                                                                        fileType)
                                                                }
                                                            })
        }
    }

    // 文件解密
    function fileDecrypt(fileId) {
        const c_fileDecrypt = Qt.createComponent("qrc:/qml/FileDecrypt.qml")

        if (c_fileDecrypt.status === Component.Ready) {
            root.showPopupCenter(c_fileDecrypt)
            popupCenter.loadercenter.item.fileId = fileId
            popupCenter.loadercenter.item.closeFile.connect(() => {
                                                                if (mainDockLoader.currentEditorName
                                                                    === "multiEditor") {
                                                                    mainDockLoader.item.closeFile(
                                                                        fileId,
                                                                        fileName,
                                                                        fileType)
                                                                }
                                                            })
        }
    }

    // 确认查看加密文件
    function confirmLookEncryotFile(nodeId, eventName, password = "") {
        root.projectTreeModuleDoubleSelected(nodeId, eventName, password)

        if (password.length > 0) {
            if (popupCenter.loadercenter.item) {
                popupCenter.loadercenter.item.result = readEncryptFileResult
            }
        }
    }

    // 查看加密文件
    function lookEncryotFile(nodeId, eventName, lock) {
        if (lock) {
            const c_lookEncryptFile = Qt.createComponent(
                                        "qrc:/qml/LookEncryptFile.qml")

            if (c_lookEncryptFile.status === Component.Ready) {
                root.showPopupCenter(c_lookEncryptFile)
                popupCenter.loadercenter.item.modelId = nodeId
                popupCenter.loadercenter.item.modelEventName = eventName
                popupCenter.loadercenter.item.confirmPas.connect(
                            root.confirmLookEncryotFile)
            }
        } else {
            confirmLookEncryotFile(nodeId, eventName)
        }
    }

    //冷启动
    function actionColdBoot() {
        commond.coldStart()
    }

    //热启动
    function actionHotBoot() {
        commond.hotStart()
    }

    //停止
    function actionStop() {
        commond.stop()
    }

    //重启
    function actionRestart() {
        commond.restart()
    }

    // 全局快捷键信号对象
    QtObject {
        id: shortcutController


        /**
         * 复制
         * handler 回调方法,需要将对应的内容添加到剪贴板中
         */
        signal copy(var handler)
        // 剪切
        signal shear(var handler)

        // 粘贴
        signal paste(var handler)

        // 撤销
        signal undo(var handler)
        // 反撤销
        signal redo(var handler)

        // 清除剪切标识
        signal clearShear()

        // 删除
        signal del()

        // LD独有
        // 左粘贴
        signal leftPaste(var handler)
        // 下粘贴
        signal lowerPaste(var handler)
        // 多输入
        signal multipleInput()
        // 多输出
        signal multipleOutput()
        // 置反
        signal reverse()
        // 置位/复位
        signal setOrReset()


        /**
         * 添加内容到快照中
         * snapshot: 需要添加的内容
         */
        function addSnapshot(snapshot) {
            multi_shortcuts.addSnapshot(snapshot)
        }


        /**
         * 获取快照
         * fileKey: 文件名
         * deviceName: 设备名称
         * direction: 方向,向前获取还是向后获取 true:向后 false:向前
         * 只有在文件名和设备名称相同的情况下才能获取
         */
        function getSnapshot(fileKey, deviceName, direction) {
            return multi_shortcuts.getSnapshot(fileKey, deviceName, direction)
        }


        /**
         * 添加内容到剪贴板中
         * content: 需要添加的内容
         */
        function addContentToClipboard(content) {
            multi_shortcuts.addContentToClipboard(content)
        }


        /**
         * 获取剪贴板内容如果是剪切的内容则修改粘贴次数
         * fileType: 文件类型
         * deviceName: 设备名称
         * contentType: 内容类型
         * 只有在文件类型、设备名称、内容类型相同的情况下才能获取
         */
        function getClipbooardContent(fileType, deviceName, contentType) {
            return multi_shortcuts.getClipbooardContent(fileType, deviceName,
                                                        contentType)
        }


        /**
         * 获取剪贴板内容
         * fileType: 文件类型
         * deviceName: 设备名称
         * contentType: 内容类型
         * 只有在文件类型、设备名称、内容类型相同的情况下才能获取
         */
        function getClipbooardContentWithoutCount(fileType, deviceName, contentType) {
            return multi_shortcuts.getClipbooardContentWithoutCount(fileType,
                                                                    deviceName,
                                                                    contentType)
        }


        /**
         * 清除指定文件中剪切的内容
         * deviceName: 设备名称
         * fileName: 文件名
         */
        function clearShearContent(deviceName, fileName) {
            multi_shortcuts.clearShearContent(deviceName, fileName)
        }
    }

    // 快捷键
    MultiShortcuts {
        id: multi_shortcuts
    }

    width: 1920
    height: 1080
    visible: true
    title: qsTr("ReliAUTO Studio") + " " + projectPath
           + (trans ? trans.transString : "") + (isSave ? " *" : "")

    Connections {
        target: serviceInterface
        function onProjectPathChanged(path) {
            projectPath = path
        }
    }

    Component.onCompleted: {
        //绑定Dock
        mainLayout.addDockWidget(leftDock, KDDW.KDDockWidgets.Location_OnLeft,
                                 null, Qt.size(100, 800),
                                 KDDW.KDDockWidgets.StartVisible)
        mainLayout.addDockWidget(rightDock,
                                 KDDW.KDDockWidgets.Location_OnRight, null,
                                 Qt.size(300, 800),
                                 KDDW.KDDockWidgets.StartVisible)
        mainLayout.addDockWidget(mainDock, KDDW.KDDockWidgets.Location_OnRight,
                                 leftDock, Qt.size(1500, 700),
                                 KDDW.KDDockWidgets.StartVisible)
        mainLayout.addDockWidget(bottomDock,
                                 KDDW.KDDockWidgets.Location_OnBottom,
                                 mainDock, Qt.size(1500, 200),
                                 KDDW.KDDockWidgets.StartVisible)
        //layoutSaver.saveToFile("DefaultSavedLayoutSet.json")
        //恢复布局
        layoutSaver.restoreFromFile("DefaultSavedLayoutSet.json")
        rightDock.open()
        statusSyncTimer.start()
    }

    //窗口关闭时
    onClosing: function (closeevent) {
        layoutSaver.saveToFile("DefaultSavedLayoutSet.json")
        //点击标题栏关闭按钮，弹一个退出提示框
        //proc.kill_process_mfc()
        projectAndFileManage.closeProject()
        console.log("quit.....")
    }

    //功能窗口
    KDDW.DockingArea {
        id: mainLayout

        anchors.fill: parent
        uniqueName: "mainLayout"

        //左侧Dock
        KDDW.DockWidget {
            id: leftDock
            uniqueName: "leftDock "
            title: qsTr("Project Tree") + (trans ? trans.transString : "")

            Loader {
                id: leftDockLoader
                anchors.fill: parent
            }
        }

        //项目树
        Component {
            id: c_projectTree

            ProjectTree {
                id: projectTree
                enableReClcik: true
                enableRightKeyMenu: true
                Component.onCompleted: {
                    //绑定数据
                    //root.projectTreeDataBind(projectTree)
                    //连接信号
                    projectTree.childNodeClicked.connect(
                                root.projectTreeModuleSelected)
                    projectTree.childNodeDoubleClicked.connect(
                                root.projectTreeModuleDoubleSelected)
                }

                contentMenu: QkMenu {
                    // 右键菜单
                    id: contentMenu

                    property var itemObj

                    QkMenuItem {
                        enabled: {
                            if (contentMenu.itemObj
                                    && contentMenu.itemObj.nodeType === "file") {
                                // 文件id
                                const fileId = projectAndFileManage.getFileInfoFromProjectTreeID(
                                                 contentMenu.itemObj.Id).id
                                if (!projectAndFileManage.isEncryptedFile(
                                            fileId)) {
                                    return true
                                }
                            }

                            return false
                        }
                        text: qsTr("Encryption") + (trans ? trans.transString : "")
                        onTriggered: {
                            const fileInfo = projectAndFileManage.getFileInfoFromProjectTreeID(
                                               contentMenu.itemObj.Id)
                            root.fileEncryot(fileInfo.id, fileInfo.fileName,
                                             fileInfo.childType)
                        }
                    }

                    QkMenuItem {
                        enabled: {
                            if (contentMenu.itemObj
                                    && contentMenu.itemObj.nodeType === "file") {
                                // 文件id
                                const fileId = projectAndFileManage.getFileInfoFromProjectTreeID(
                                                 contentMenu.itemObj.Id).id

                                if (projectAndFileManage.isEncryptedFile(
                                            fileId)) {
                                    return true
                                }
                            }

                            return false
                        }
                        text: qsTr("Decryption") + (trans ? trans.transString : "")
                        onTriggered: {
                            const fileInfo = projectAndFileManage.getFileInfoFromProjectTreeID(
                                               contentMenu.itemObj.Id)
                            root.fileDecrypt(fileInfo.id, fileInfo.fileName,
                                             fileInfo.childType)
                        }
                    }

                    QkMenuItem {
                        enabled: contentMenu.itemObj ? (contentMenu.itemObj.nodeType
                                                        === "device") : false
                        text: qsTr("Activating Device") + (trans ? trans.transString : "")
                        onTriggered: {
                            console.log("Activating Device",
                                        contentMenu.itemObj.Id,
                                        contentMenu.itemObj.Name,
                                        contentMenu.itemObj.EventName)
                            //激活设备组态
                            if (projectAndFileManage.setSelectedNode(
                                        contentMenu.itemObj.Id))
                                //GlobalVariable.activedDevice = contentMenu.itemObj.Name
                                root.projectTreeDataBind(leftDockLoader.item)
                            else
                                console.log("激活设备失败")
                        }
                    }

                    QkMenuItem {
                        enabled: contentMenu.itemObj ? (contentMenu.itemObj.nodeType
                                                        === "device") : false
                        text: qsTr("Delete Device") + (trans ? trans.transString : "")
                        onTriggered: {
                            console.log("Delete Device",
                                        contentMenu.itemObj.Id,
                                        contentMenu.itemObj.Name,
                                        contentMenu.itemObj.EventName)
                            //删除该组态设备
                            messageDialog.show(
                                        "是否删除该设备" + contentMenu.itemObj.Name
                                        + ",相关的组态配置和程序将会一并删除?", function () {
                                            //先删除设备组态
                                            DeviceAndNetworkManage.deleteDevice(
                                                        contentMenu.itemObj.Name)
                                            VariableManage.deleteDeviceDataType(
                                                        contentMenu.itemObj.Name)
                                            projectAndFileManage.deleteDeviceNode(
                                                        contentMenu.itemObj.Id)
                                            root.gv.isDSCheckCompleted = false
                                            closeAllEditingDock()
                                            root.projectTreeDataBind(
                                                        leftDockLoader.item)
                                        }, function () {
                                            console.log("confrim no1")
                                        }, "confirm")
                        }
                    }

                    QkMenuItem {
                        enabled: contentMenu.itemObj ? (contentMenu.itemObj.nodeType === "file"
                                                        || contentMenu.itemObj.nodeType
                                                        === "wavedata") : false
                        text: qsTr("Delete File") + (trans ? trans.transString : "")
                        onTriggered: {
                            console.log("Delete File", contentMenu.itemObj.Id,
                                        contentMenu.itemObj.Name,
                                        contentMenu.itemObj.nodeType,
                                        contentMenu.itemObj.EventName)
                            //删除该文件
                            messageDialog.show(
                                        "是否删除该文件" + contentMenu.itemObj.Name + "?",
                                        function () {
                                            if (contentMenu.itemObj.nodeType === "file") {
                                                //程序文件删除 根据项目树节点ID返回文件信息
                                                const fileinfo = projectAndFileManage.getFileInfoFromProjectTreeID(
                                                                   contentMenu.itemObj.Id)
                                                //根据文件ID取消文件权限
                                                if (!UserGroupManage.removeFilePer(
                                                            fileinfo.id))
                                                    messageDialog.show(
                                                                "文件权限取消失败",
                                                                function () {//点击确认后的回调函数
                                                                })
                                                //先删除文件
                                                projectAndFileManage.deleteFileFromProjectTreeNode(
                                                            contentMenu.itemObj.Id)
                                                //再删除该文件的变量
                                                VariableManage.deleteVariableByOwned(
                                                            fileinfo.settingName,
                                                            fileinfo.fileName)
                                                //删除功能块引用表中的引用信息
                                                let fbName = fileinfo.fileName.split(
                                                        ".")[0]
                                                VariableManage.deleteFbreferenceSettingName(
                                                            fileinfo.settingName,
                                                            fileinfo.fileName)
                                                //关闭当前编辑面板
                                                closeAllEditingDock()
                                                //重新绑定项目树
                                                root.projectTreeDataBind(
                                                            leftDockLoader.item)
                                            } else if (contentMenu.itemObj.nodeType
                                                       === "wavedata") {
                                                if (!waveRecordInterface.getWaveFlag(
                                                            )) {
                                                    //录波数据删除
                                                    if (waveManage.deleteWaveSet(
                                                                contentMenu.itemObj.Name)) {
                                                        //删除项目树节点
                                                        projectAndFileManage.deleteProjectTreeNode(
                                                                    contentMenu.itemObj.Id)
                                                        //关闭当前编辑面板
                                                        closeAllEditingDock()
                                                        //重新绑定项目树
                                                        root.projectTreeDataBind(
                                                                    leftDockLoader.item)
                                                    } else {
                                                        console.log("delete wave file error",
                                                                    contentMenu.itemObj.Id,
                                                                    contentMenu.itemObj.Name)
                                                    }
                                                } else {
                                                    messageDialog.show(
                                                                "在线录波中，无法删除录波文件!")
                                                }
                                            }
                                        }, function () {
                                            console.log("confrim no1")
                                        }, "confirm")
                        }
                    }
                }
            }
        }

        //主窗口
        KDDW.DockWidget {
            id: mainDock

            uniqueName: "mainDock"
            title: qsTr("Main Dock") + (trans ? trans.transString : "")
            onIsOpenChanged: {

            }

            Loader {
                id: mainDockLoader

                property string currentEditorName: ""

                anchors.fill: parent
            }
        }

        //设备组态
        Component {
            id: c_deviceSet

            DeviceSetEditor {
                id: deviceSet

                Component.onCompleted: {
                    //deviceSet.getBindData()
                    deviceSet.slotSelected.connect(root.deviceSetSlotSelected)
                    deviceSet.dataChanged.connect(root.deviceSetDataChange)
                }
            }
        }

        //模块变量
        Component {
            id: c_moduleVariable

            ModuleVariable {
                id: moduleVariable

                Component.onCompleted: {

                }
            }
        }

        //设备网络图
        Component {
            id: c_devicenetworkShow

            DNShow {

                //devicenetworkShow.getBindData()
                id: devicenetworkShow

                Component.onCompleted: {
                    mainDock.title = qsTr(
                                "Device Network Show") + (trans ? trans.transString : "")
                }
            }
        }

        Component {
            id: c_multiEditor
            MultiEditor {
                id: multiEditor
                Component.onCompleted: {
                    mainDock.title = qsTr(
                                "Code Editor") + (trans ? trans.transString : "")
                }
            }
        }

        //故障信息
        Component {
            id: c_errorInfoTableShow

            ErrorInfo {
                id: errorInfoTableShow

                Component.onCompleted: {
                    mainDock.title = qsTr(
                                "Error Info") + (trans ? trans.transString : "")
                    errorInfoTableShow.getBindData()
                }
            }
        }

        //变量资源池
        Component {
            id: c_variableListShow
            FVariableList {
                id: variableListShow
                Component.onCompleted: {
                    mainDock.title = qsTr(
                                "Variable List Show") + (trans ? trans.transString : "")
                }
            }
        }

        Component {
            id: c_dataTypeManageShow
            FVariableType {
                id: dataTypeManageShow
                Component.onCompleted: {
                    mainDock.title = qsTr(
                                "DataType Manage") + (trans ? trans.transString : "")
                }
            }
        }

        //监视强制M表
        Component {
            id: c_monitorMShow
            FMonitorList {
                id: monitorMShow
                Component.onCompleted: {
                    mainDock.title = qsTr(
                                "Monitor M") + (trans ? trans.transString : "")
                }
            }
        }

        // //监视强制自定义表
        Component {
            id: c_monitorCustomShow
            FForcedList {
                id: monitorCustomShow
                Component.onCompleted: {
                    mainDock.title = qsTr(
                                "Monitor Custom") + (trans ? trans.transString : "")
                }
            }
        }
    }

    //程序信息表
    Component {
        id: c_programInfoShow
        ProgramInfo {
            id: programInfoShow

            Component.onCompleted: {
                mainDock.title = qsTr(
                            "Program Info") + (trans ? trans.transString : "")
            }
        }
    }

    //地址分配表
    Component {
        id: c_addressInfoShow
        AddressInfo {
            id: addressInfoShow
            Component.onCompleted: {
                mainDock.title = qsTr(
                            "Address Info") + (trans ? trans.transString : "")
            }
        }
    }

    //CFC编辑器
    Component {
        id: c_cfcEditor
        MultiCFCEditor {
            id: cfcEditor
            Component.onCompleted: {
                mainDock.title = qsTr(
                            "CFC Editor") + (trans ? trans.transString : "")
            }
        }
    }

    // //LD编辑器
    Component {
        id: c_ldEditor
        MultiLDEditor {
            id: ldEditor
            Component.onCompleted: {
                mainDock.title = qsTr(
                            "LD Editor") + (trans ? trans.transString : "")
            }
        }
    }

    // //FBD编辑器
    Component {
        id: c_fbdEditor
        MultiNEWFBDEditor {
            id: fbdEditor
            Component.onCompleted: {
                mainDock.title = qsTr(
                            "Graph Editor") + (trans ? trans.transString : "")
            }
        }
    }

    //SFC编辑器
    Component {
        id: c_sfcEditor
        MultiSFCEditor {
            id: sfcEditor
            Component.onCompleted: {
                mainDock.title = qsTr(
                            "Graph Editor") + (trans ? trans.transString : "")
            }
        }
    }

    //SOE上半部分表格
    Component {
        id: c_soeTableShow
        SoeTable {
            Component.onCompleted: {
                mainDock.title = qsTr(
                            "SOE Show") + (trans ? trans.transString : "")
            }
        }
    }

    //SOE下半部分历史
    Component {
        id: c_soeRecordShow
        SoeRecord {
            Component.onCompleted: {

                // bottomDock.title = qsTr(
                //             "SOE Record") + (trans ? trans.transString : "")
            }
        }
    }

    // 录波图
    Component {
        id: c_waveCanvas
        WaveCanvas {
            onShowRightDockChanged: {
                if (showRightDock) {
                    let waveCanvas = mainDockLoader.item
                    rightDock.open()
                    waveCanvas.measureCursor = rightTabView.getComponent(4)
                } else {
                    rightDock.close()
                }
            }
            onErrorMessageChanged: {
                messageDialog.show(errMessage)
            }
            Component.onCompleted: {
                mainDock.title = qsTr(
                            "Wave cnavas") + (trans ? trans.transString : "")
            }
        }
    }

    //轨迹上半部分表格
    Component {
        id: c_trackListShow
        TrackTable {
            Component.onCompleted: {
                mainDock.title = qsTr(
                            "Track Show") + (trans ? trans.transString : "")
            }
        }
    }

    //轨迹下半部分图表
    Component {
        id: c_trackWaveChatShow
        TrackChart {
            Component.onCompleted: {

                // bottomDock.title = qsTr(
                //             "Track chart") + (trans ? trans.transString : "")
            }
        }
    }

    //右侧
    KDDW.DockWidget {
        id: rightDock
        uniqueName: "rightDock"
        title: qsTr("rightDock") + (trans ? trans.transString : "")
        TabViewControl {
            id: rightTabView
            anchors.fill: parent

            Component.onCompleted: {
                rightTabView.addDynamicTab("设备管理器", template)
                rightTabView.addDynamicTab("指令管理器", template)
                rightTabView.addDynamicTab("库管理器", template)
                rightTabView.addDynamicTab("查找和替换", template)
                rightTabView.addDynamicTab("测量光标", template)
            }
        }
    }
    //设备树
    Component {
        id: c_deviceTree
        DeviceTreeSelector {
            id: deviceTree

            Component.onCompleted: {
                deviceTree.deviceSelected.connect(root.devictTreeModuleAdd)
            }
        }
    }

    //固件函数与功能块列表
    Component {
        id: c_functionblockList
        FunctionBlockList {
            id: functionblockList
        }
    }

    Component {
        id: c_measure_cursor
        MeasureCursor {
            id: measure_cursor
            onXAxisChanged: {
                let waveCanvas = mainDockLoader.item
                waveCanvas.updateXAxisData(xAxisData)
            }
            onYAxisChanged: {
                let waveCanvas = mainDockLoader.item
                waveCanvas.updateYAxisData(yAxisData)
            }
        }
    }

    //消息窗口视图底部
    KDDW.DockWidget {
        id: bottomDock
        title: qsTr("Message View") + (trans ? trans.transString : "")
        uniqueName: "Message View"
        TabViewControl {
            id: bottomTabView
            anchors.fill: parent
            tabPosition: Qt.BottomEdge

            Component.onCompleted: {
                bottomTabView.addDynamicTab("属性", template)
                bottomTabView.addDynamicTab("日志信息", template)
                bottomTabView.addDynamicTab("引用信息", template)
                bottomTabView.addDynamicTab("交叉引用信息", template)
            }
        }
    }

    Component {
        id: template
        Rectangle {}
    }

    Component {
        id: uploadDialogComponent
        Rectangle {
            width: 400
            height: 120
            QkButtonRow {
                id: title
                z: 10
                QkLabel {
                    anchors.left: parent.left
                    anchors.leftMargin: 10
                    anchors.verticalCenter: parent.verticalCenter
                    text: qsTr("Upload") + (trans ? trans.transString : "")
                }

                Rectangle {
                    width: 20
                    height: 20
                    radius: 6
                    anchors.right: parent.right
                    anchors.rightMargin: 10
                    anchors.verticalCenter: parent.verticalCenter

                    Text {
                        anchors.centerIn: parent
                        text: "×"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                        font.pixelSize: 16
                    }

                    MouseArea {
                        anchors.fill: parent
                        cursorShape: Qt.PointingHandCursor
                        onClicked: {
                            popupCenter.close()
                        }
                    }
                }
            }
            // 修改后的按钮区域
            RowLayout {
                width: parent.width
                anchors.bottom: parent.bottom
                anchors.bottomMargin: 30
                spacing: 0

                // 左侧占位弹簧
                Item {
                    Layout.fillWidth: true
                }

                QkButton {
                    text: qsTr("Upload Program") + (trans ? trans.transString : "")
                    Layout.preferredWidth: 160
                    onClicked: {
                        root.actionUploadProgram()
                    }
                }

                Item {
                    Layout.fillWidth: true
                }

                QkButton {
                    isGradientBgColor: true
                    text: qsTr("Upload Hardware") + (trans ? trans.transString : "")
                    Layout.preferredWidth: 160
                    onClicked: {
                        root.actionUploadHardware()
                    }
                }

                // 右侧占位弹簧
                Item {
                    Layout.fillWidth: true
                }
            }
        }
    }

    Component {
        id: downloadDialogComponent
        Rectangle {
            width: 400
            height: 120
            QkButtonRow {
                id: title
                z: 10
                QkLabel {
                    anchors.left: parent.left
                    anchors.leftMargin: 10

                    anchors.verticalCenter: parent.verticalCenter
                    text: qsTr("Download") + (trans ? trans.transString : "")
                }

                Rectangle {
                    width: 20
                    height: 20
                    radius: 6
                    anchors.right: parent.right
                    anchors.rightMargin: 10
                    anchors.verticalCenter: parent.verticalCenter

                    Text {
                        anchors.centerIn: parent
                        text: "×"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                        font.pixelSize: 16
                    }

                    MouseArea {
                        anchors.fill: parent
                        cursorShape: Qt.PointingHandCursor
                        onClicked: {
                            popupCenter.close()
                        }
                    }
                }
            }
            // 修改后的按钮区域
            RowLayout {
                width: parent.width
                anchors.bottom: parent.bottom
                anchors.bottomMargin: 30
                spacing: 0

                // 左侧占位弹簧
                Item {
                    Layout.fillWidth: true
                }

                // 关闭按钮
                QkButton {
                    text: qsTr("Download To RAM") + (trans ? trans.transString : "")
                    Layout.preferredWidth: 150
                    Layout.alignment: Qt.AlignVCenter
                    onClicked: {
                        root.actionDownloadRam()
                    }
                }

                // 右侧占位弹簧
                Item {
                    Layout.fillWidth: true
                }

                // 确认按钮
                QkButton {
                    isGradientBgColor: true
                    text: qsTr("Download To Flash") + (trans ? trans.transString : "")
                    Layout.preferredWidth: 150
                    Layout.alignment: Qt.AlignVCenter
                    onClicked: {
                        root.actionDownloadSystem()
                    }
                }

                // 右侧占位弹簧
                Item {
                    Layout.fillWidth: true
                }
            }
        }
    }

    Component {
        id: c_waveSetting
        WaveSetting {
            id: waveSetting
            onWaveVarChanged: {
                let waveCanvas = mainDockLoader.item
                if (waveCanvas instanceof WaveCanvas) {
                    waveCanvas.setWaveVariable()
                }
            }
            onWaveConChanged: {
                let waveCanvas = mainDockLoader.item
                if (waveCanvas instanceof WaveCanvas) {
                    waveCanvas.setWaveConfig(true)
                }
            }
        }
    }

    //设备组态属性编辑
    Component {
        id: c_attributeEdit

        AttributeEdit {
            id: attributeEdit

            Component.onCompleted: {
                // bottomDock.title = qsTr(
                //             "Attribute Editing") + (trans ? trans.transString : "")
                attributeEdit.firmwareUpdateClick.connect(
                            root.showFirmwareUpdateDialog)
            }
        }
    }

    //设备网络显示连接编辑
    Component {
        id: c_deviceNetworkEdit

        DeviceNetworkEdit {
            id: deviceNetworkEdit

            Component.onCompleted: {
                // bottomDock.title = qsTr(
                //             "Device Network Editing") + (trans ? trans.transString : "")
                deviceNetworkEdit.deviceNetworkValueChanged.connect(
                            root.deviceNetworkValueChanged)
            }
        }
    }

    // 报警信息导出文件选择
    Labs11.FolderDialog {
        id: exportDialog
        title: qsTr("Export") + (trans ? trans.transString : "")
        acceptLabel: qsTr("Confirm") + (trans ? trans.transString : "")
        rejectLabel: qsTr("Cancel") + (trans ? trans.transString : "")
        onAccepted: {
            let path = folder.toString()
            if (path.startsWith("file:///")) {
                path = decodeURI(path.substring(8))
            }
            OutputManage.exportLogFile(path)
        }
    }

    // 工程另存为
    Labs11.FileDialog {
        id: savaProjectPath
        title: qsTr("Project Save As") + (trans ? trans.transString : "")
        currentFile: "file:///"
        acceptLabel: qsTr("Confirm") + (trans ? trans.transString : "")
        rejectLabel: qsTr("Cancel") + (trans ? trans.transString : "")
        nameFilters: ["TGIC project File(*.proj)"]
        fileMode: Labs11.FileDialog.SaveFile
        onAccepted: {
            var dstPath = String(savaProjectPath.currentFile)
            if (Qt.platform.os === "windows")
                dstPath = dstPath.substr(8)

            // 分开路径和文件名
            const path = dstPath.substr(0, dstPath.lastIndexOf("/") + 1)
            const filename = dstPath.substr(dstPath.lastIndexOf(
                                                "/") + 1).replace(".proj", "")
            const flag = projectAndFileManage.saveProjectAs(filename, path)
            if (flag) {
                messageDialog.show(
                            qsTr("Save Success") + (trans ? trans.transString : ""))

                const projectInfo = projectAndFileManage.getCurrentProjectInfo()
                projectPath = projectInfo.ProjectPath

                mainMenuBar.refreshRecentList()
                leftDockLoader.sourceComponent = c_projectTree
                root.projectTreeDataBind(leftDockLoader.item)
            } else {
                messageDialog.show(
                            qsTr("Save Failed") + (trans ? trans.transString : ""))
            }
        }
    }

    //布局保存
    KDDW.LayoutSaver {
        id: layoutSaver
    }

    SingletonWindow {
        id: singletonWindow
        width: 1280
        height: 800
        title: qsTr("Security module") + (trans ? trans.transString : "")
        contentComponent: Component {
            SecurityModule {}
        }
    }

    //弹出窗口
    MainPopup {
        id: popupCenter
    }

    //打开项目面板
    Labs11.FileDialog {
        id: openProjectDialog

        title: qsTr("Open Project") + (trans ? trans.transString : "")
        acceptLabel: qsTr("Open") + (trans ? trans.transString : "")
        rejectLabel: qsTr("Cancel") + (trans ? trans.transString : "")
        nameFilters: ["TGIC Project File(*.proj)"]
        fileMode: Labs11.FileDialog.OpenFile
        onAccepted: {
            console.log("Open Project", file)

            //发送后端 这里的file实际为绝对路径
            if (serviceInterface.openProject(file)) {
                //根据返回结果进行当前项目切换
                root.openedProject()
            } else {
                messageDialog.show("工程打开错误，请先检查工程路径以及工程内容是否有误！", function () {
                    //确认后关闭对话框
                    popupCenter.close()
                })
            }
        }
    }

    FluContentDialog {
        id: messageDialog

        property var okfunc
        property var nofunc

        title: qsTr("Tip") + (trans ? trans.transString : "")
        message: qsTr("Input Error") + (trans ? trans.transString : "")
        negativeText: "取消"
        positiveText: "确定"
        buttonFlags: FluContentDialogType.PositiveButton
        onPositiveClicked: {
            if (okfunc) {
                okfunc()
            }
        }
        onNegativeClicked: {
            if (nofunc) {
                nofunc()
            }
        }
        function show(caption, funcok, funcno, type = "info") {
            messageDialog.okfunc = funcok
            messageDialog.nofunc = funcno
            if (type === "info") {
                messageDialog.buttonFlags = FluContentDialogType.PositiveButton
            } else if (type === "confirm") {
                messageDialog.buttonFlags = FluContentDialogType.NegativeButton
                        | FluContentDialogType.PositiveButton
            }
            messageDialog.message = caption
            messageDialog.open()
        }
    }

    //字体加载
    FontLoader {
        id: localFont

        source: "qrc:/assets/font/SourceHanSansCN-Regular_0.otf"
    }

    //状态同步定时器，每秒同步
    Timer {
        id: statusSyncTimer
        repeat: true
        interval: 1000
        onTriggered: {
            if (GlobalVariable.isConnectedPLC !== targetPLC.isLinked())
                GlobalVariable.isDSCheckCompleted = false

            GlobalVariable.isConnectedPLC = targetPLC.isLinked()
            GlobalVariable.isSimulation = commond.simulate_Flag()
            GlobalVariable.isOnline = commond.getConnectFlag()
            GlobalVariable.isPLCRunning = commond.getRunningFlag()
            root.stateChange()
        }
    }

    //    Universal.theme: Universal.Dark
    //    Universal.accent: Universal.Violet
    //菜单栏
    menuBar: MainMenuBar {
        id: mainMenuBar

        Component.onCompleted: {
            menuDataBind(mainMenuBar)
            //日志页默认不展开
            // logDock.close()
            mainMenuBar.menuItemTriggered.connect(root.menuItemTriggered)
            mainMenuBar.changeState(",close,")
            //加载最近项目列表
            mainMenuBar.refreshRecentList()
        }
    }

    //工具栏
    header: MainToolBar {
        id: mainToolBar

        Component.onCompleted: {
            toolBarDataBind(mainToolBar)
            mainToolBar.toolBarTriggered.connect(root.toolBarTriggered)
            mainToolBar.changeState(",close,")

            // 7天内最近一次打开的文件
            var lastOpenFilePath = serviceInterface.getLastOpenFile()
            if (lastOpenFilePath) {
                // 程序启动时打开7天内最近一次打开的文件
                if (serviceInterface.openProject(lastOpenFilePath))
                    root.openedProject()
            }
        }
    }

    //状态栏
    footer: MainStatusBar {
        id: mainStatusBar
    }
}
